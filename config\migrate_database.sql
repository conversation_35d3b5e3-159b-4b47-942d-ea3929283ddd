-- Database Migration Script for Attendance System
-- Run this script to update your existing database with missing columns

USE attendance_system;

-- Add description column to departments table if it doesn't exist
ALTER TABLE departments
ADD COLUMN IF NOT EXISTS description TEXT AFTER code;

-- Add description column to courses table if it doesn't exist
ALTER TABLE courses
ADD COLUMN IF NOT EXISTS description TEXT AFTER credits;

-- Create semesters table if it doesn't exist
CREATE TABLE IF NOT EXISTS semesters (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(50) NOT NULL,
    code VARCHAR(10) UNIQUE NOT NULL,
    start_date DATE,
    end_date DATE,
    academic_year VARCHAR(10) NOT NULL,
    is_active BOOLEAN DEFAULT TRUE,
    is_current BOOLEAN DEFAULT FALSE,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default semesters if they don't exist
INSERT IGNORE INTO semesters (name, code, start_date, end_date, academic_year, is_active, is_current, description) VALUES
('Fall Semester', 'FALL', '2024-08-15', '2024-12-15', '2024-2025', TRUE, TRUE, 'Fall semester for academic year 2024-2025'),
('Spring Semester', 'SPRING', '2025-01-15', '2025-05-15', '2024-2025', TRUE, FALSE, 'Spring semester for academic year 2024-2025'),
('Summer Semester', 'SUMMER', '2025-06-01', '2025-07-31', '2024-2025', TRUE, FALSE, 'Summer semester for academic year 2024-2025');

-- Add semester_id column to classes table if it doesn't exist
ALTER TABLE classes
ADD COLUMN IF NOT EXISTS semester_id INT AFTER section;

-- Add foreign key constraint for semester_id if it doesn't exist
SET @constraint_exists = (SELECT COUNT(*) FROM information_schema.KEY_COLUMN_USAGE
                         WHERE TABLE_SCHEMA = DATABASE()
                         AND TABLE_NAME = 'classes'
                         AND CONSTRAINT_NAME = 'classes_ibfk_3');

SET @sql = IF(@constraint_exists = 0,
    'ALTER TABLE classes ADD CONSTRAINT classes_ibfk_3 FOREIGN KEY (semester_id) REFERENCES semesters(id)',
    'SELECT "Foreign key constraint already exists" as message');

PREPARE stmt FROM @sql;
EXECUTE stmt;
DEALLOCATE PREPARE stmt;

-- Add schedule column to classes table if it doesn't exist
ALTER TABLE classes
ADD COLUMN IF NOT EXISTS schedule VARCHAR(100) AFTER academic_year;

-- Add room column to classes table if it doesn't exist
ALTER TABLE classes
ADD COLUMN IF NOT EXISTS room VARCHAR(50) AFTER schedule;

-- Create settings table if it doesn't exist
CREATE TABLE IF NOT EXISTS settings (
    id INT PRIMARY KEY AUTO_INCREMENT,
    setting_key VARCHAR(100) UNIQUE NOT NULL,
    setting_value TEXT,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Insert default settings if they don't exist
INSERT IGNORE INTO settings (setting_key, setting_value, description) VALUES
('institute_name', 'Sanskar Institute of Management & Information Technology', 'Name of the institute'),
('academic_year', '2024-2025', 'Current academic year'),
('attendance_threshold', '75', 'Minimum attendance percentage required'),
('late_threshold', '15', 'Minutes after start time to mark as late'),
('timezone', 'Asia/Kolkata', 'System timezone'),
('email_notifications', '1', 'Enable email notifications'),
('sms_notifications', '0', 'Enable SMS notifications'),
('auto_backup', '1', 'Enable automatic backups'),
('backup_frequency', 'daily', 'Backup frequency'),
('session_timeout', '30', 'Session timeout in minutes'),
('max_login_attempts', '5', 'Maximum login attempts'),
('password_min_length', '8', 'Minimum password length'),
('require_password_change', '90', 'Password change required in days');

-- Migrate existing semester data to semester_id
UPDATE classes c
JOIN semesters s ON (
    (c.semester = 'Fall' AND s.code = 'FALL') OR
    (c.semester = 'Spring' AND s.code = 'SPRING') OR
    (c.semester = 'Summer' AND s.code = 'SUMMER')
)
SET c.semester_id = s.id
WHERE c.semester_id IS NULL AND c.semester IS NOT NULL;

-- Set default semester_id for classes without semester data
UPDATE classes c
SET c.semester_id = (SELECT id FROM semesters WHERE code = 'FALL' LIMIT 1)
WHERE c.semester_id IS NULL;

-- Update existing classes to have default semester if NULL (for backward compatibility)
UPDATE classes SET semester = 'Fall' WHERE semester IS NULL;

-- Display success message
SELECT 'Database migration completed successfully!' as message;
SELECT 'All missing columns have been added.' as info;
