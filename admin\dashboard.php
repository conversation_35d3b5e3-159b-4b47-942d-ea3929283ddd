<?php
session_start();
require_once '../config/database.php';

// Check if user is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../auth/login.php');
    exit();
}

$page_title = 'Admin Dashboard';

// Get statistics
try {
    // Total users by role
    $stmt = $db->query("SELECT role, COUNT(*) as count FROM users WHERE is_active = 1 GROUP BY role");
    $user_stats = $stmt->fetchAll(PDO::FETCH_KEY_PAIR);

    // Total departments
    $stmt = $db->query("SELECT COUNT(*) FROM departments");
    $total_departments = $stmt->fetchColumn();

    // Total courses
    $stmt = $db->query("SELECT COUNT(*) FROM courses");
    $total_courses = $stmt->fetchColumn();

    // Total classes
    $stmt = $db->query("SELECT COUNT(*) FROM classes");
    $total_classes = $stmt->fetchColumn();

    // Recent attendance sessions
    $stmt = $db->query("
        SELECT
            ats.date,
            ats.start_time,
            ats.end_time,
            c.name as course_name,
            u.full_name as teacher_name,
            cl.section
        FROM attendance_sessions ats
        JOIN classes cl ON ats.class_id = cl.id
        JOIN courses c ON cl.course_id = c.id
        JOIN users u ON cl.teacher_id = u.id
        ORDER BY ats.date DESC, ats.start_time DESC
        LIMIT 5
    ");
    $recent_sessions = $stmt->fetchAll(PDO::FETCH_ASSOC);

} catch (PDOException $e) {
    $error = "Database error: " . $e->getMessage();
}

include '../includes/header.php';
?>

<div class="px-4 py-6 sm:px-0">
    <!-- Page Header -->
    <div class="mb-8">
        <h1 class="text-3xl font-bold text-gray-900">Admin Dashboard</h1>
        <p class="mt-2 text-gray-600">Welcome back, <?php echo htmlspecialchars($_SESSION['full_name']); ?>!</p>
    </div>

    <!-- Statistics Cards -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <!-- Total Students -->
        <div class="bg-white overflow-hidden shadow-lg rounded-lg">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-blue-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-user-graduate text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Students</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $user_stats['student'] ?? 0; ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Teachers -->
        <div class="bg-white overflow-hidden shadow-lg rounded-lg">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-green-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-chalkboard-teacher text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Teachers</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $user_stats['teacher'] ?? 0; ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Courses -->
        <div class="bg-white overflow-hidden shadow-lg rounded-lg">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-purple-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-book text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Courses</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $total_courses; ?></p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Total Departments -->
        <div class="bg-white overflow-hidden shadow-lg rounded-lg">
            <div class="p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <div class="w-8 h-8 bg-orange-500 rounded-md flex items-center justify-center">
                            <i class="fas fa-building text-white text-sm"></i>
                        </div>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Departments</p>
                        <p class="text-2xl font-bold text-gray-900"><?php echo $total_departments; ?></p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Actions -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
        <!-- Management Actions -->
        <div class="bg-white shadow-lg rounded-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-cogs mr-2 text-indigo-600"></i>
                Management
            </h3>
            <div class="grid grid-cols-2 gap-4">
                <a href="users.php" class="flex items-center p-4 bg-blue-50 rounded-lg hover:bg-blue-100 transition duration-200">
                    <i class="fas fa-users text-blue-600 mr-3"></i>
                    <span class="text-sm font-medium text-blue-900">Manage Users</span>
                </a>
                <a href="students.php" class="flex items-center p-4 bg-indigo-50 rounded-lg hover:bg-indigo-100 transition duration-200">
                    <i class="fas fa-user-graduate text-indigo-600 mr-3"></i>
                    <span class="text-sm font-medium text-indigo-900">Manage Students</span>
                </a>
                <a href="departments.php" class="flex items-center p-4 bg-green-50 rounded-lg hover:bg-green-100 transition duration-200">
                    <i class="fas fa-building text-green-600 mr-3"></i>
                    <span class="text-sm font-medium text-green-900">Departments</span>
                </a>
                <a href="courses.php" class="flex items-center p-4 bg-purple-50 rounded-lg hover:bg-purple-100 transition duration-200">
                    <i class="fas fa-book text-purple-600 mr-3"></i>
                    <span class="text-sm font-medium text-purple-900">Courses</span>
                </a>
                <a href="semesters.php" class="flex items-center p-4 bg-teal-50 rounded-lg hover:bg-teal-100 transition duration-200">
                    <i class="fas fa-calendar-alt text-teal-600 mr-3"></i>
                    <span class="text-sm font-medium text-teal-900">Semesters</span>
                </a>
                <a href="classes.php" class="flex items-center p-4 bg-orange-50 rounded-lg hover:bg-orange-100 transition duration-200">
                    <i class="fas fa-chalkboard text-orange-600 mr-3"></i>
                    <span class="text-sm font-medium text-orange-900">Classes</span>
                </a>
            </div>
        </div>

        <!-- Reports -->
        <div class="bg-white shadow-lg rounded-lg p-6">
            <h3 class="text-lg font-semibold text-gray-900 mb-4">
                <i class="fas fa-chart-bar mr-2 text-indigo-600"></i>
                Reports & Analytics
            </h3>
            <div class="grid grid-cols-2 gap-4">
                <a href="reports/attendance.php" class="flex items-center p-4 bg-red-50 rounded-lg hover:bg-red-100 transition duration-200">
                    <i class="fas fa-chart-line text-red-600 mr-3"></i>
                    <span class="text-sm font-medium text-red-900">Attendance Reports</span>
                </a>
                <a href="reports/students.php" class="flex items-center p-4 bg-yellow-50 rounded-lg hover:bg-yellow-100 transition duration-200">
                    <i class="fas fa-user-graduate text-yellow-600 mr-3"></i>
                    <span class="text-sm font-medium text-yellow-900">Student Reports</span>
                </a>
                <a href="reports/teachers.php" class="flex items-center p-4 bg-indigo-50 rounded-lg hover:bg-indigo-100 transition duration-200">
                    <i class="fas fa-chalkboard-teacher text-indigo-600 mr-3"></i>
                    <span class="text-sm font-medium text-indigo-900">Teacher Reports</span>
                </a>
                <a href="settings.php" class="flex items-center p-4 bg-gray-50 rounded-lg hover:bg-gray-100 transition duration-200">
                    <i class="fas fa-cog text-gray-600 mr-3"></i>
                    <span class="text-sm font-medium text-gray-900">Settings</span>
                </a>
            </div>
        </div>
    </div>

    <!-- Recent Activity -->
    <div class="bg-white shadow-lg rounded-lg">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">
                <i class="fas fa-clock mr-2 text-indigo-600"></i>
                Recent Attendance Sessions
            </h3>
        </div>
        <div class="p-6">
            <?php if (!empty($recent_sessions)): ?>
                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-gray-200">
                        <thead class="bg-gray-50">
                            <tr>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Date</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Time</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Course</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Teacher</th>
                                <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Section</th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-gray-200">
                            <?php foreach ($recent_sessions as $session): ?>
                                <tr>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo date('M d, Y', strtotime($session['date'])); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo date('h:i A', strtotime($session['start_time'])) . ' - ' . date('h:i A', strtotime($session['end_time'])); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo htmlspecialchars($session['course_name']); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo htmlspecialchars($session['teacher_name']); ?>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                        <?php echo htmlspecialchars($session['section']); ?>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
            <?php else: ?>
                <div class="text-center py-8">
                    <i class="fas fa-calendar-times text-gray-400 text-4xl mb-4"></i>
                    <p class="text-gray-500">No recent attendance sessions found.</p>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<?php include '../includes/footer.php'; ?>
