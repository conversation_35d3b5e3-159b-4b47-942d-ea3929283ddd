<?php
session_start();
require_once '../config/database.php';

// Check if user is admin
if (!isset($_SESSION['user_id']) || $_SESSION['role'] !== 'admin') {
    header('Location: ../auth/login.php');
    exit();
}

$page_title = 'Semester Management';
$success = '';
$error = '';

// Handle form submissions
if ($_POST) {
    if ($_POST['action'] === 'add_semester') {
        $name = trim($_POST['name']);
        $code = trim(strtoupper($_POST['code']));
        $start_date = $_POST['start_date'];
        $end_date = $_POST['end_date'];
        $academic_year = $_POST['academic_year'];
        $description = trim($_POST['description']);
        $is_current = isset($_POST['is_current']) ? 1 : 0;

        if (!empty($name) && !empty($code) && !empty($academic_year)) {
            try {
                // Check if code already exists
                $stmt = $db->prepare("SELECT id FROM semesters WHERE code = :code");
                $stmt->bindParam(':code', $code);
                $stmt->execute();

                if ($stmt->rowCount() > 0) {
                    $error = "Semester code already exists";
                } else {
                    // If this is set as current, unset all other current semesters
                    if ($is_current) {
                        $db->exec("UPDATE semesters SET is_current = FALSE");
                    }

                    // Insert new semester
                    $stmt = $db->prepare("
                        INSERT INTO semesters (name, code, start_date, end_date, academic_year, description, is_current, created_at)
                        VALUES (:name, :code, :start_date, :end_date, :academic_year, :description, :is_current, NOW())
                    ");
                    $stmt->bindParam(':name', $name);
                    $stmt->bindParam(':code', $code);
                    $stmt->bindParam(':start_date', $start_date);
                    $stmt->bindParam(':end_date', $end_date);
                    $stmt->bindParam(':academic_year', $academic_year);
                    $stmt->bindParam(':description', $description);
                    $stmt->bindParam(':is_current', $is_current);
                    $stmt->execute();

                    $success = "Semester added successfully!";
                }
            } catch (PDOException $e) {
                $error = "Database error: " . $e->getMessage();
            }
        } else {
            $error = "Please fill in all required fields";
        }
    } elseif ($_POST['action'] === 'edit_semester') {
        $id = $_POST['semester_id'];
        $name = trim($_POST['name']);
        $code = trim(strtoupper($_POST['code']));
        $start_date = $_POST['start_date'];
        $end_date = $_POST['end_date'];
        $academic_year = $_POST['academic_year'];
        $description = trim($_POST['description']);
        $is_current = isset($_POST['is_current']) ? 1 : 0;
        $is_active = isset($_POST['is_active']) ? 1 : 0;

        if (!empty($name) && !empty($code) && !empty($academic_year)) {
            try {
                // Check if code already exists for other semesters
                $stmt = $db->prepare("SELECT id FROM semesters WHERE code = :code AND id != :id");
                $stmt->bindParam(':code', $code);
                $stmt->bindParam(':id', $id);
                $stmt->execute();

                if ($stmt->rowCount() > 0) {
                    $error = "Semester code already exists";
                } else {
                    // If this is set as current, unset all other current semesters
                    if ($is_current) {
                        $db->exec("UPDATE semesters SET is_current = FALSE WHERE id != $id");
                    }

                    // Update semester
                    $stmt = $db->prepare("
                        UPDATE semesters
                        SET name = :name, code = :code, start_date = :start_date, end_date = :end_date,
                            academic_year = :academic_year, description = :description,
                            is_current = :is_current, is_active = :is_active
                        WHERE id = :id
                    ");
                    $stmt->bindParam(':name', $name);
                    $stmt->bindParam(':code', $code);
                    $stmt->bindParam(':start_date', $start_date);
                    $stmt->bindParam(':end_date', $end_date);
                    $stmt->bindParam(':academic_year', $academic_year);
                    $stmt->bindParam(':description', $description);
                    $stmt->bindParam(':is_current', $is_current);
                    $stmt->bindParam(':is_active', $is_active);
                    $stmt->bindParam(':id', $id);
                    $stmt->execute();

                    $success = "Semester updated successfully!";
                }
            } catch (PDOException $e) {
                $error = "Database error: " . $e->getMessage();
            }
        } else {
            $error = "Please fill in all required fields";
        }
    } elseif ($_POST['action'] === 'delete_semester') {
        $id = $_POST['semester_id'];

        try {
            // Check if semester has classes
            $stmt = $db->prepare("SELECT COUNT(*) FROM classes WHERE semester_id = :id");
            $stmt->bindParam(':id', $id);
            $stmt->execute();
            $class_count = $stmt->fetchColumn();

            if ($class_count > 0) {
                $error = "Cannot delete semester. It has $class_count associated classes.";
            } else {
                // Delete semester
                $stmt = $db->prepare("DELETE FROM semesters WHERE id = :id");
                $stmt->bindParam(':id', $id);
                $stmt->execute();

                $success = "Semester deleted successfully!";
            }
        } catch (PDOException $e) {
            $error = "Error deleting semester: " . $e->getMessage();
        }
    } elseif ($_POST['action'] === 'set_current') {
        $id = $_POST['semester_id'];

        try {
            // Unset all current semesters
            $db->exec("UPDATE semesters SET is_current = FALSE");

            // Set this semester as current
            $stmt = $db->prepare("UPDATE semesters SET is_current = TRUE WHERE id = :id");
            $stmt->bindParam(':id', $id);
            $stmt->execute();

            $success = "Current semester updated successfully!";
        } catch (PDOException $e) {
            $error = "Error updating current semester: " . $e->getMessage();
        }
    }
}

// Get all semesters
try {
    $stmt = $db->query("
        SELECT s.*, COUNT(c.id) as class_count
        FROM semesters s
        LEFT JOIN classes c ON s.id = c.semester_id
        GROUP BY s.id
        ORDER BY s.academic_year DESC, s.start_date DESC
    ");
    $semesters = $stmt->fetchAll(PDO::FETCH_ASSOC);
} catch (PDOException $e) {
    $error = "Error fetching semesters: " . $e->getMessage();
}

include '../includes/header.php';
?>

<div class="px-4 py-6 sm:px-0">
    <!-- Page Header -->
    <div class="mb-8 flex justify-between items-center">
        <div>
            <h1 class="text-3xl font-bold text-gray-900">Semester Management</h1>
            <p class="mt-2 text-gray-600">Manage academic semesters and terms</p>
        </div>
        <button onclick="toggleAddModal()" class="bg-indigo-600 hover:bg-indigo-700 text-white px-4 py-2 rounded-lg">
            <i class="fas fa-plus mr-2"></i>Add Semester
        </button>
    </div>

    <!-- Success/Error Messages -->
    <?php if ($success): ?>
        <div class="mb-6 bg-green-50 border border-green-200 text-green-700 px-4 py-3 rounded-md alert-auto-hide">
            <div class="flex">
                <i class="fas fa-check-circle mt-0.5 mr-2"></i>
                <span><?php echo htmlspecialchars($success); ?></span>
            </div>
        </div>
    <?php endif; ?>

    <?php if ($error): ?>
        <div class="mb-6 bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-md">
            <div class="flex">
                <i class="fas fa-exclamation-circle mt-0.5 mr-2"></i>
                <span><?php echo htmlspecialchars($error); ?></span>
            </div>
        </div>
    <?php endif; ?>

    <!-- Semesters Table -->
    <div class="bg-white shadow-lg rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-gray-200">
            <h3 class="text-lg font-semibold text-gray-900">All Semesters</h3>
        </div>
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Semester</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Duration</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Academic Year</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Classes</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                    <?php foreach ($semesters as $semester): ?>
                        <tr>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div>
                                    <div class="text-sm font-medium text-gray-900"><?php echo htmlspecialchars($semester['name']); ?></div>
                                    <div class="text-sm text-gray-500"><?php echo htmlspecialchars($semester['code']); ?></div>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php if ($semester['start_date'] && $semester['end_date']): ?>
                                    <?php echo date('M j, Y', strtotime($semester['start_date'])); ?> -
                                    <?php echo date('M j, Y', strtotime($semester['end_date'])); ?>
                                <?php else: ?>
                                    <span class="text-gray-400">Not set</span>
                                <?php endif; ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                                <?php echo htmlspecialchars($semester['academic_year']); ?>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <div class="flex space-x-2">
                                    <?php if ($semester['is_current']): ?>
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-green-100 text-green-800">
                                            Current
                                        </span>
                                    <?php endif; ?>
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full <?php echo $semester['is_active'] ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'; ?>">
                                        <?php echo $semester['is_active'] ? 'Active' : 'Inactive'; ?>
                                    </span>
                                </div>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap">
                                <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-purple-100 text-purple-800">
                                    <?php echo $semester['class_count']; ?> Class<?php echo $semester['class_count'] != 1 ? 'es' : ''; ?>
                                </span>
                            </td>
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                <?php if (!$semester['is_current']): ?>
                                    <form method="POST" class="inline mr-2">
                                        <input type="hidden" name="action" value="set_current">
                                        <input type="hidden" name="semester_id" value="<?php echo $semester['id']; ?>">
                                        <button type="submit" class="text-green-600 hover:text-green-900"
                                                onclick="return confirmAction('Set this as current semester?')">
                                            Set Current
                                        </button>
                                    </form>
                                <?php endif; ?>
                                <button onclick="editSemester(<?php echo $semester['id']; ?>, '<?php echo htmlspecialchars($semester['name'], ENT_QUOTES); ?>', '<?php echo htmlspecialchars($semester['code'], ENT_QUOTES); ?>', '<?php echo $semester['start_date']; ?>', '<?php echo $semester['end_date']; ?>', '<?php echo htmlspecialchars($semester['academic_year'], ENT_QUOTES); ?>', '<?php echo htmlspecialchars($semester['description'], ENT_QUOTES); ?>', <?php echo $semester['is_current'] ? 'true' : 'false'; ?>, <?php echo $semester['is_active'] ? 'true' : 'false'; ?>)"
                                        class="text-indigo-600 hover:text-indigo-900 mr-3">
                                    Edit
                                </button>
                                <?php if ($semester['class_count'] == 0): ?>
                                    <form method="POST" class="inline">
                                        <input type="hidden" name="action" value="delete_semester">
                                        <input type="hidden" name="semester_id" value="<?php echo $semester['id']; ?>">
                                        <button type="submit" class="text-red-600 hover:text-red-900"
                                                onclick="return confirmAction('Are you sure you want to delete this semester?')">
                                            Delete
                                        </button>
                                    </form>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Add/Edit Semester Modal -->
<div id="semesterModal" class="fixed inset-0 bg-gray-600 bg-opacity-50 hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg shadow-xl max-w-lg w-full">
            <div class="px-6 py-4 border-b border-gray-200">
                <h3 id="modalTitle" class="text-lg font-semibold text-gray-900">Add Semester</h3>
            </div>
            <form method="POST" class="p-6">
                <input type="hidden" id="modalAction" name="action" value="add_semester">
                <input type="hidden" id="semesterId" name="semester_id" value="">

                <div class="space-y-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Semester Name</label>
                        <input type="text" id="semesterName" name="name" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="Fall Semester">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Semester Code</label>
                        <input type="text" id="semesterCode" name="code" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="FALL">
                    </div>

                    <div class="grid grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">Start Date</label>
                            <input type="date" id="semesterStartDate" name="start_date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        </div>

                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-1">End Date</label>
                            <input type="date" id="semesterEndDate" name="end_date" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500">
                        </div>
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Academic Year</label>
                        <input type="text" id="semesterYear" name="academic_year" required class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="2024-2025" value="2024-2025">
                    </div>

                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Description</label>
                        <textarea id="semesterDescription" name="description" rows="3" class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-indigo-500 focus:border-indigo-500" placeholder="Optional description"></textarea>
                    </div>

                    <div class="space-y-2">
                        <div class="flex items-center">
                            <input type="checkbox" id="semesterCurrent" name="is_current" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                            <label for="semesterCurrent" class="ml-2 block text-sm text-gray-900">Set as current semester</label>
                        </div>

                        <div id="activeCheckboxDiv" class="flex items-center" style="display: none;">
                            <input type="checkbox" id="semesterActive" name="is_active" class="h-4 w-4 text-indigo-600 focus:ring-indigo-500 border-gray-300 rounded">
                            <label for="semesterActive" class="ml-2 block text-sm text-gray-900">Active semester</label>
                        </div>
                    </div>
                </div>

                <div class="flex justify-end space-x-3 mt-6">
                    <button type="button" onclick="toggleAddModal()" class="px-4 py-2 text-gray-700 bg-gray-200 rounded-md hover:bg-gray-300">
                        Cancel
                    </button>
                    <button type="submit" id="submitBtn" class="px-4 py-2 bg-indigo-600 text-white rounded-md hover:bg-indigo-700">
                        Add Semester
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<script>
function toggleAddModal() {
    const modal = document.getElementById('semesterModal');
    modal.classList.toggle('hidden');

    // Reset form for add mode
    document.getElementById('modalTitle').textContent = 'Add Semester';
    document.getElementById('modalAction').value = 'add_semester';
    document.getElementById('submitBtn').textContent = 'Add Semester';
    document.getElementById('semesterId').value = '';
    document.getElementById('semesterName').value = '';
    document.getElementById('semesterCode').value = '';
    document.getElementById('semesterStartDate').value = '';
    document.getElementById('semesterEndDate').value = '';
    document.getElementById('semesterYear').value = '2024-2025';
    document.getElementById('semesterDescription').value = '';
    document.getElementById('semesterCurrent').checked = false;
    document.getElementById('semesterActive').checked = true;
    document.getElementById('activeCheckboxDiv').style.display = 'none';
}

function editSemester(id, name, code, startDate, endDate, year, description, isCurrent, isActive) {
    const modal = document.getElementById('semesterModal');
    modal.classList.remove('hidden');

    // Set form for edit mode
    document.getElementById('modalTitle').textContent = 'Edit Semester';
    document.getElementById('modalAction').value = 'edit_semester';
    document.getElementById('submitBtn').textContent = 'Update Semester';
    document.getElementById('semesterId').value = id;
    document.getElementById('semesterName').value = name;
    document.getElementById('semesterCode').value = code;
    document.getElementById('semesterStartDate').value = startDate;
    document.getElementById('semesterEndDate').value = endDate;
    document.getElementById('semesterYear').value = year;
    document.getElementById('semesterDescription').value = description;
    document.getElementById('semesterCurrent').checked = isCurrent;
    document.getElementById('semesterActive').checked = isActive;
    document.getElementById('activeCheckboxDiv').style.display = 'flex';
}

function confirmAction(message) {
    return confirm(message);
}

// Auto-hide success messages
document.addEventListener('DOMContentLoaded', function() {
    const alerts = document.querySelectorAll('.alert-auto-hide');
    alerts.forEach(function(alert) {
        setTimeout(function() {
            alert.style.opacity = '0';
            setTimeout(function() {
                alert.remove();
            }, 300);
        }, 5000);
    });
});
</script>

<?php include '../includes/footer.php'; ?>
