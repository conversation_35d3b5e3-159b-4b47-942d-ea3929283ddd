<?php
// Navigation helper functions and components

/**
 * Get the current page information for navigation
 */
function getCurrentPageInfo() {
    $current_file = basename($_SERVER['PHP_SELF']);
    $current_dir = basename(dirname($_SERVER['PHP_SELF']));

    $pages = [
        // Admin pages
        'admin' => [
            'dashboard.php' => ['title' => 'Dashboard', 'icon' => 'fas fa-tachometer-alt'],
            'users.php' => ['title' => 'User Management', 'icon' => 'fas fa-users'],
            'students.php' => ['title' => 'Student Management', 'icon' => 'fas fa-user-graduate'],
            'departments.php' => ['title' => 'Departments', 'icon' => 'fas fa-building'],
            'courses.php' => ['title' => 'Courses', 'icon' => 'fas fa-book'],
            'semesters.php' => ['title' => 'Semesters', 'icon' => 'fas fa-calendar-alt'],
            'classes.php' => ['title' => 'Classes', 'icon' => 'fas fa-chalkboard'],
            'settings.php' => ['title' => 'Settings', 'icon' => 'fas fa-cog'],
            'student-details.php' => ['title' => 'Student Details', 'icon' => 'fas fa-user-graduate'],
        ],
        'reports' => [
            'attendance.php' => ['title' => 'Attendance Reports', 'icon' => 'fas fa-chart-line'],
            'students.php' => ['title' => 'Student Reports', 'icon' => 'fas fa-user-graduate'],
            'teachers.php' => ['title' => 'Teacher Reports', 'icon' => 'fas fa-chalkboard-teacher'],
        ],
        // Teacher pages
        'teacher' => [
            'dashboard.php' => ['title' => 'Dashboard', 'icon' => 'fas fa-tachometer-alt'],
            'mark-attendance.php' => ['title' => 'Mark Attendance', 'icon' => 'fas fa-clipboard-check'],
            'view-attendance.php' => ['title' => 'View Attendance', 'icon' => 'fas fa-eye'],
            'attendance-reports.php' => ['title' => 'Attendance Reports', 'icon' => 'fas fa-chart-bar'],
            'my-classes.php' => ['title' => 'My Classes', 'icon' => 'fas fa-chalkboard'],
            'students.php' => ['title' => 'Students', 'icon' => 'fas fa-user-graduate'],
            'profile.php' => ['title' => 'Profile', 'icon' => 'fas fa-user-cog'],
        ],
        // Student pages
        'student' => [
            'dashboard.php' => ['title' => 'Dashboard', 'icon' => 'fas fa-tachometer-alt'],
            'my-attendance.php' => ['title' => 'My Attendance', 'icon' => 'fas fa-calendar-check'],
            'attendance-reports.php' => ['title' => 'Attendance Reports', 'icon' => 'fas fa-chart-bar'],
            'class-schedule.php' => ['title' => 'Class Schedule', 'icon' => 'fas fa-calendar'],
            'my-classes.php' => ['title' => 'My Classes', 'icon' => 'fas fa-chalkboard'],
            'teachers.php' => ['title' => 'Teachers', 'icon' => 'fas fa-chalkboard-teacher'],
            'profile.php' => ['title' => 'Profile', 'icon' => 'fas fa-user-cog'],
        ]
    ];

    // Handle reports subdirectory
    if ($current_dir === 'reports') {
        $parent_dir = 'admin'; // Reports are under admin
        $page_info = $pages['reports'][$current_file] ?? null;
    } else {
        $parent_dir = $current_dir;
        $page_info = $pages[$current_dir][$current_file] ?? null;
    }

    return [
        'current_file' => $current_file,
        'current_dir' => $current_dir,
        'parent_dir' => $parent_dir,
        'page_info' => $page_info
    ];
}

/**
 * Generate breadcrumb navigation
 */
function generateBreadcrumb() {
    $info = getCurrentPageInfo();
    $role = $_SESSION['role'] ?? 'guest';

    $breadcrumb = '<nav class="flex mb-6" aria-label="Breadcrumb">';
    $breadcrumb .= '<ol class="inline-flex items-center space-x-1 md:space-x-3">';

    // Home/Dashboard link
    $dashboard_url = ($info['current_dir'] === 'reports') ? '../dashboard.php' : 'dashboard.php';
    $breadcrumb .= '<li class="inline-flex items-center">';
    $breadcrumb .= '<a href="' . $dashboard_url . '" class="inline-flex items-center text-sm font-medium text-gray-700 hover:text-indigo-600">';
    $breadcrumb .= '<i class="fas fa-home mr-2"></i>';
    $breadcrumb .= ucfirst($role) . ' Dashboard';
    $breadcrumb .= '</a>';
    $breadcrumb .= '</li>';

    // Current page (if not dashboard)
    if ($info['current_file'] !== 'dashboard.php') {
        $breadcrumb .= '<li>';
        $breadcrumb .= '<div class="flex items-center">';
        $breadcrumb .= '<i class="fas fa-chevron-right text-gray-400 mx-2"></i>';

        // Handle reports subdirectory
        if ($info['current_dir'] === 'reports') {
            $breadcrumb .= '<span class="text-sm font-medium text-gray-500 mr-2">Reports</span>';
            $breadcrumb .= '<i class="fas fa-chevron-right text-gray-400 mx-2"></i>';
        }

        if ($info['page_info']) {
            $breadcrumb .= '<span class="text-sm font-medium text-gray-500">';
            $breadcrumb .= '<i class="' . $info['page_info']['icon'] . ' mr-2"></i>';
            $breadcrumb .= $info['page_info']['title'];
            $breadcrumb .= '</span>';
        } else {
            $breadcrumb .= '<span class="text-sm font-medium text-gray-500">';
            $breadcrumb .= ucfirst(str_replace(['.php', '-'], ['', ' '], $info['current_file']));
            $breadcrumb .= '</span>';
        }

        $breadcrumb .= '</div>';
        $breadcrumb .= '</li>';
    }

    $breadcrumb .= '</ol>';
    $breadcrumb .= '</nav>';

    return $breadcrumb;
}

/**
 * Generate back button navigation
 */
function generateBackButton() {
    $info = getCurrentPageInfo();
    $role = $_SESSION['role'] ?? 'guest';

    // Don't show back button on dashboard
    if ($info['current_file'] === 'dashboard.php') {
        return '';
    }

    $back_url = '';
    $back_text = '';
    $back_icon = 'fas fa-arrow-left';

    // Determine back navigation based on current location
    if ($info['current_dir'] === 'reports') {
        $back_url = '../dashboard.php';
        $back_text = 'Back to Admin Dashboard';
    } else {
        $back_url = 'dashboard.php';
        $back_text = 'Back to ' . ucfirst($role) . ' Dashboard';
    }

    $back_button = '<div class="mb-6">';
    $back_button .= '<a href="' . $back_url . '" class="inline-flex items-center px-4 py-2 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 hover:text-indigo-600 transition duration-200">';
    $back_button .= '<i class="' . $back_icon . ' mr-2"></i>';
    $back_button .= $back_text;
    $back_button .= '</a>';
    $back_button .= '</div>';

    return $back_button;
}

/**
 * Generate role-specific navigation menu
 */
function generateRoleNavigation() {
    $role = $_SESSION['role'] ?? 'guest';
    $info = getCurrentPageInfo();

    $nav_items = [];

    switch ($role) {
        case 'admin':
            $nav_items = [
                ['url' => 'dashboard.php', 'title' => 'Dashboard', 'icon' => 'fas fa-tachometer-alt'],
                ['url' => 'users.php', 'title' => 'Users', 'icon' => 'fas fa-users'],
                ['url' => 'students.php', 'title' => 'Students', 'icon' => 'fas fa-user-graduate'],
                ['url' => 'departments.php', 'title' => 'Departments', 'icon' => 'fas fa-building'],
                ['url' => 'courses.php', 'title' => 'Courses', 'icon' => 'fas fa-book'],
                ['url' => 'semesters.php', 'title' => 'Semesters', 'icon' => 'fas fa-calendar-alt'],
                ['url' => 'classes.php', 'title' => 'Classes', 'icon' => 'fas fa-chalkboard'],
                ['url' => 'reports/attendance.php', 'title' => 'Reports', 'icon' => 'fas fa-chart-line'],
                ['url' => 'settings.php', 'title' => 'Settings', 'icon' => 'fas fa-cog'],
            ];
            break;

        case 'teacher':
            $nav_items = [
                ['url' => 'dashboard.php', 'title' => 'Dashboard', 'icon' => 'fas fa-tachometer-alt'],
                ['url' => 'mark-attendance.php', 'title' => 'Mark Attendance', 'icon' => 'fas fa-clipboard-check'],
                ['url' => 'view-attendance.php', 'title' => 'View Attendance', 'icon' => 'fas fa-eye'],
                ['url' => 'my-classes.php', 'title' => 'My Classes', 'icon' => 'fas fa-chalkboard'],
                ['url' => 'students.php', 'title' => 'Students', 'icon' => 'fas fa-user-graduate'],
                ['url' => 'attendance-reports.php', 'title' => 'Reports', 'icon' => 'fas fa-chart-bar'],
                ['url' => 'profile.php', 'title' => 'Profile', 'icon' => 'fas fa-user-cog'],
            ];
            break;

        case 'student':
            $nav_items = [
                ['url' => 'dashboard.php', 'title' => 'Dashboard', 'icon' => 'fas fa-tachometer-alt'],
                ['url' => 'my-attendance.php', 'title' => 'My Attendance', 'icon' => 'fas fa-calendar-check'],
                ['url' => 'my-classes.php', 'title' => 'My Classes', 'icon' => 'fas fa-chalkboard'],
                ['url' => 'class-schedule.php', 'title' => 'Schedule', 'icon' => 'fas fa-calendar'],
                ['url' => 'teachers.php', 'title' => 'Teachers', 'icon' => 'fas fa-chalkboard-teacher'],
                ['url' => 'attendance-reports.php', 'title' => 'Reports', 'icon' => 'fas fa-chart-bar'],
                ['url' => 'profile.php', 'title' => 'Profile', 'icon' => 'fas fa-user-cog'],
            ];
            break;
    }

    if (empty($nav_items)) {
        return '';
    }

    $navigation = '<div class="bg-white shadow-sm border-b border-gray-200 mb-6">';
    $navigation .= '<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">';
    $navigation .= '<div class="flex space-x-8 overflow-x-auto">';

    foreach ($nav_items as $item) {
        $is_active = false;

        // Adjust URL based on current directory
        $item_url = $item['url'];
        if ($info['current_dir'] === 'reports') {
            // If we're in reports directory, adjust URLs
            if (strpos($item['url'], 'reports/') === 0) {
                $item_url = basename($item['url']); // Remove reports/ prefix since we're already in reports
            } else {
                $item_url = '../' . $item['url']; // Go back to admin directory
            }
        }

        // Check if current page matches
        if ($info['current_dir'] === 'reports' && strpos($item['url'], 'reports/') === 0) {
            $is_active = (basename($item['url']) === $info['current_file']);
        } else {
            $is_active = (basename($item['url']) === $info['current_file']);
        }

        $active_class = $is_active ? 'border-indigo-500 text-indigo-600' : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300';

        $navigation .= '<a href="' . $item_url . '" class="whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm ' . $active_class . '">';
        $navigation .= '<i class="' . $item['icon'] . ' mr-2"></i>';
        $navigation .= $item['title'];
        $navigation .= '</a>';
    }

    $navigation .= '</div>';
    $navigation .= '</div>';
    $navigation .= '</div>';

    return $navigation;
}

/**
 * Generate complete navigation for a page
 */
function generatePageNavigation() {
    $role_nav = generateRoleNavigation();
    $breadcrumb = generateBreadcrumb();
    $back_button = generateBackButton();

    return $role_nav . $back_button . $breadcrumb;
}
?>
